"""
Comprehensive tests for MailBot algorithms.

This module contains tests for all algorithm implementations (V1, V2, V3),
the base algorithm class, helper functions, and utility functions.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
from django.test import TestCase
from django.db.models import Count, Q

from mailbot.algorithms.base import BaseAlgorithm
from mailbot.algorithms.v1_algorithm import V1Algorithm
from mailbot.algorithms.v2_algorithm import V2Algorithm
from mailbot.algorithms.v3_algorithm import V3Algorithm
from mailbot.algorithms.helpers import (
    get_category_messages,
    is_fts_treatment_disabled,
    should_show_fts_overlay,
)
from mailbot.algorithms.utils import (
    get_algorithm_instance,
    get_default_active_algorithm_def,
)
from mailbot.algorithms.constants import ALG<PERSON>ITHM_MAP, DEFAULT_ALGORITHM_VERSION
from mailbot.models import (
    AlgorithmDefinition,
    AlgorithmVersion,
    AlgorithmCategoryScore,
    MessageCategory,
    MailBotGenericLabel,
    SenderProfile,
    UserMailBotProfile,
    Message,
)
from mailbot.tests.factories import (
    UserMailBotProfileFactory,
    SenderProfileFactory,
    MessageFactory,
)


class MockAlgorithmDefinition:
    """Mock AlgorithmDefinition for testing."""

    def __init__(self, version=1, is_active=True):
        self.version = version
        self.is_active = is_active


class MockMessageCategory:
    """Mock MessageCategory for testing."""

    def __init__(self, id=1, name="Test Category", score=1):
        self.id = id
        self.name = name
        self.score = score


class MockSenderProfile:
    """Mock SenderProfile for testing."""

    def __init__(self, read_count=5, total_count=10, metadata=None):
        self.read_count = read_count
        self.total_count = total_count
        self.metadata = metadata or {}
        self.user_mailbot_profile = Mock()
        self.user_mailbot_profile.preferences = {}
        self.user_mailbot_profile.is_onboarding_invalid = False


class MockUserMailBotProfile:
    """Mock UserMailBotProfile for testing."""

    def __init__(self, preferences=None, algorithm_definition=None):
        self.preferences = preferences or {}
        self.algorithm_definition = algorithm_definition


class BaseAlgorithmTests(TestCase):
    """Tests for the BaseAlgorithm abstract class."""

    def setUp(self):
        """Set up test fixtures."""
        self.algo_def = MockAlgorithmDefinition()

        # Create a concrete implementation for testing
        class ConcreteAlgorithm(BaseAlgorithm):
            def get_total_message_counts(self, sender_profile, mailbot_profile_id, message_categories):
                return 10

        self.algorithm = ConcreteAlgorithm(self.algo_def)

    def test_init(self):
        """Test algorithm initialization."""
        self.assertEqual(self.algorithm.algo_def, self.algo_def)

    def test_version_property(self):
        """Test version property returns algorithm definition version."""
        self.assertEqual(self.algorithm.version, 1)

    def test_has_important_categories_true(self):
        """Test _has_important_categories returns True for score >= 2."""
        categories = [MockMessageCategory(score=2)]
        with patch.object(self.algorithm, "_get_total_category_score", return_value=2):
            self.assertTrue(self.algorithm._has_important_categories(categories))

    def test_has_important_categories_false(self):
        """Test _has_important_categories returns False for score < 2."""
        categories = [MockMessageCategory(score=1)]
        with patch.object(self.algorithm, "_get_total_category_score", return_value=1):
            self.assertFalse(self.algorithm._has_important_categories(categories))

    @patch("mailbot.models.AlgorithmCategoryScore.objects.filter")
    def test_get_total_category_score(self, mock_filter):
        """Test _get_total_category_score calculation."""
        # Mock the database query
        mock_score = Mock()
        mock_score.category_id = 1
        mock_score.score = 3
        mock_filter.return_value = [mock_score]

        categories = [MockMessageCategory(id=1, score=2)]
        result = self.algorithm._get_total_category_score(categories)

        # Should use algorithm-specific score (3) instead of default (2)
        self.assertEqual(result, 3)

    def test_default_implementations(self):
        """Test default implementations of optional methods."""
        sender_profile = MockSenderProfile()
        categories = [MockMessageCategory()]
        user_profile = MockUserMailBotProfile()

        # Test default implementations
        self.assertFalse(self.algorithm.should_send_fts_overlay(sender_profile))
        self.assertFalse(self.algorithm.should_show_critical_alert_overlay(categories))
        self.assertEqual(self.algorithm.default_action_label(user_profile, categories), MailBotGenericLabel.ZAPPED)
        self.assertEqual(
            self.algorithm.should_whitelist_read_fraction_action(sender_profile, 1, categories), (False, False)
        )
        self.assertEqual(self.algorithm.get_open_rate(sender_profile, 1, categories), 0)


class V1AlgorithmTests(TestCase):
    """Tests for the V1Algorithm implementation."""

    def setUp(self):
        """Set up test fixtures."""
        self.algo_def = MockAlgorithmDefinition()
        self.algorithm = V1Algorithm(self.algo_def)

    def test_default_action_label_inbox_treatment(self):
        """Test default_action_label with inbox treatment."""
        user_profile = MockUserMailBotProfile(preferences={"first_time_sender_treatment": "inbox"})
        categories = [MockMessageCategory()]

        result = self.algorithm.default_action_label(user_profile, categories)
        self.assertEqual(result, MailBotGenericLabel.WHITE_LIST)

    def test_default_action_label_other_treatment(self):
        """Test default_action_label with non-inbox treatment."""
        user_profile = MockUserMailBotProfile(preferences={"first_time_sender_treatment": "low_priority"})
        categories = [MockMessageCategory()]

        result = self.algorithm.default_action_label(user_profile, categories)
        self.assertEqual(result, MailBotGenericLabel.ZAPPED)

    def test_default_action_label_missing_preference(self):
        """Test default_action_label with missing preference."""
        user_profile = MockUserMailBotProfile(preferences={})
        categories = [MockMessageCategory()]

        result = self.algorithm.default_action_label(user_profile, categories)
        self.assertEqual(result, MailBotGenericLabel.ZAPPED)

    def test_should_show_critical_alert_overlay(self):
        """Test should_show_critical_alert_overlay always returns True."""
        categories = [MockMessageCategory()]
        self.assertTrue(self.algorithm.should_show_critical_alert_overlay(categories))

    def test_should_whitelist_read_fraction_action_high_rate(self):
        """Test should_whitelist_read_fraction_action with high open rate."""
        sender_profile = MockSenderProfile(read_count=9, total_count=11)  # 90% open rate
        categories = [MockMessageCategory()]

        should_whitelist, include_in_digest = self.algorithm.should_whitelist_read_fraction_action(
            sender_profile, 1, categories
        )

        self.assertTrue(should_whitelist)
        self.assertFalse(include_in_digest)

    def test_should_whitelist_read_fraction_action_medium_rate(self):
        """Test should_whitelist_read_fraction_action with medium open rate."""
        sender_profile = MockSenderProfile(read_count=5, total_count=11)  # 50% open rate
        categories = [MockMessageCategory()]

        should_whitelist, include_in_digest = self.algorithm.should_whitelist_read_fraction_action(
            sender_profile, 1, categories
        )

        self.assertFalse(should_whitelist)
        self.assertTrue(include_in_digest)

    def test_should_whitelist_read_fraction_action_low_rate(self):
        """Test should_whitelist_read_fraction_action with low open rate."""
        sender_profile = MockSenderProfile(read_count=1, total_count=11)  # 10% open rate
        categories = [MockMessageCategory()]

        should_whitelist, include_in_digest = self.algorithm.should_whitelist_read_fraction_action(
            sender_profile, 1, categories
        )

        self.assertFalse(should_whitelist)
        self.assertFalse(include_in_digest)

    def test_get_total_message_counts(self):
        """Test get_total_message_counts returns sender profile total count."""
        sender_profile = MockSenderProfile(total_count=15)
        categories = [MockMessageCategory()]

        result = self.algorithm.get_total_message_counts(sender_profile, 1, categories)
        self.assertEqual(result, 15)

    def test_get_open_rate_normal_case(self):
        """Test get_open_rate calculation for normal case."""
        sender_profile = MockSenderProfile(read_count=8, total_count=11)
        categories = [MockMessageCategory()]

        result = self.algorithm.get_open_rate(sender_profile, 1, categories)
        self.assertEqual(result, 0.8)  # 8 / (11 - 1) = 0.8

    def test_get_open_rate_edge_case_total_count_one(self):
        """Test get_open_rate with total_count = 1 (edge case)."""
        sender_profile = MockSenderProfile(read_count=1, total_count=1)
        categories = [MockMessageCategory()]

        result = self.algorithm.get_open_rate(sender_profile, 1, categories)
        self.assertEqual(result, 0.0)  # Should return 0 to avoid division by zero

    def test_get_open_rate_edge_case_total_count_zero(self):
        """Test get_open_rate with total_count = 0 (edge case)."""
        sender_profile = MockSenderProfile(read_count=0, total_count=0)
        categories = [MockMessageCategory()]

        result = self.algorithm.get_open_rate(sender_profile, 1, categories)
        self.assertEqual(result, 0.0)  # Should return 0 to avoid division by zero


class V2AlgorithmTests(TestCase):
    """Tests for the V2Algorithm implementation."""

    def setUp(self):
        """Set up test fixtures."""
        self.algo_def = MockAlgorithmDefinition()
        self.algorithm = V2Algorithm(self.algo_def)

    @patch("mailbot.algorithms.helpers.is_fts_treatment_disabled")
    @patch("mailbot.algorithms.helpers.should_show_fts_overlay")
    def test_should_send_fts_overlay_enabled(self, mock_should_show, mock_is_disabled):
        """Test should_send_fts_overlay when overlay should be sent."""
        mock_is_disabled.return_value = False
        mock_should_show.return_value = True

        sender_profile = MockSenderProfile()
        result = self.algorithm.should_send_fts_overlay(sender_profile)

        self.assertTrue(result)

    @patch("mailbot.algorithms.helpers.is_fts_treatment_disabled")
    def test_should_send_fts_overlay_disabled(self, mock_is_disabled):
        """Test should_send_fts_overlay when treatment is disabled."""
        mock_is_disabled.return_value = True

        sender_profile = MockSenderProfile()
        result = self.algorithm.should_send_fts_overlay(sender_profile)

        self.assertFalse(result)

    def test_should_send_fts_overlay_invalid_onboarding(self):
        """Test should_send_fts_overlay with invalid onboarding."""
        sender_profile = MockSenderProfile()
        sender_profile.user_mailbot_profile.is_onboarding_invalid = True

        result = self.algorithm.should_send_fts_overlay(sender_profile)
        self.assertFalse(result)

    def test_should_send_fts_overlay_no_sender_profile(self):
        """Test should_send_fts_overlay with no sender profile."""
        result = self.algorithm.should_send_fts_overlay(None)
        self.assertTrue(result)

    def test_should_show_critical_alert_overlay(self):
        """Test should_show_critical_alert_overlay always returns False."""
        categories = [MockMessageCategory()]
        self.assertFalse(self.algorithm.should_show_critical_alert_overlay(categories))

    def test_default_action_label_important_categories(self):
        """Test default_action_label with important categories."""
        user_profile = MockUserMailBotProfile()
        categories = [MockMessageCategory()]

        with patch.object(self.algorithm, "_has_important_categories", return_value=True):
            result = self.algorithm.default_action_label(user_profile, categories)
            self.assertEqual(result, MailBotGenericLabel.WHITE_LIST)

    def test_default_action_label_unimportant_categories(self):
        """Test default_action_label with unimportant categories."""
        user_profile = MockUserMailBotProfile()
        categories = [MockMessageCategory()]

        with patch.object(self.algorithm, "_has_important_categories", return_value=False):
            result = self.algorithm.default_action_label(user_profile, categories)
            self.assertEqual(result, MailBotGenericLabel.ZAPPED)

    def test_should_whitelist_read_fraction_action_both_criteria_met(self):
        """Test should_whitelist_read_fraction_action when both criteria are met."""
        sender_profile = MockSenderProfile()
        categories = [MockMessageCategory()]

        with patch.object(self.algorithm, "get_open_rate", return_value=0.5):
            with patch.object(self.algorithm, "_has_important_categories", return_value=True):
                should_whitelist, include_in_digest = self.algorithm.should_whitelist_read_fraction_action(
                    sender_profile, 1, categories
                )

                self.assertTrue(should_whitelist)
                self.assertFalse(include_in_digest)

    def test_should_whitelist_read_fraction_action_high_rate_no_important(self):
        """Test should_whitelist_read_fraction_action with high rate but no important categories."""
        sender_profile = MockSenderProfile()
        categories = [MockMessageCategory()]

        with patch.object(self.algorithm, "get_open_rate", return_value=0.9):
            with patch.object(self.algorithm, "_has_important_categories", return_value=False):
                should_whitelist, include_in_digest = self.algorithm.should_whitelist_read_fraction_action(
                    sender_profile, 1, categories
                )

                self.assertFalse(should_whitelist)
                self.assertTrue(include_in_digest)

    @patch("mailbot.algorithms.helpers.get_category_messages")
    def test_get_total_message_counts(self, mock_get_messages):
        """Test get_total_message_counts uses category filtering."""
        mock_queryset = Mock()
        mock_queryset.distinct.return_value.count.return_value = 5
        mock_get_messages.return_value = mock_queryset

        sender_profile = MockSenderProfile()
        categories = [MockMessageCategory()]

        result = self.algorithm.get_total_message_counts(sender_profile, 1, categories)
        self.assertEqual(result, 5)

    @patch("mailbot.algorithms.helpers.get_category_messages")
    def test_get_open_rate(self, mock_get_messages):
        """Test get_open_rate calculation with category filtering."""
        mock_queryset = Mock()
        mock_queryset.aggregate.return_value = {"total_count": 10, "total_read": 7}
        mock_get_messages.return_value = mock_queryset

        sender_profile = MockSenderProfile()
        categories = [MockMessageCategory()]

        result = self.algorithm.get_open_rate(sender_profile, 1, categories)
        self.assertEqual(result, 0.7)

    @patch("mailbot.algorithms.helpers.get_category_messages")
    def test_get_open_rate_no_messages(self, mock_get_messages):
        """Test get_open_rate with no messages."""
        mock_queryset = Mock()
        mock_queryset.aggregate.return_value = {"total_count": 0, "total_read": 0}
        mock_get_messages.return_value = mock_queryset

        sender_profile = MockSenderProfile()
        categories = [MockMessageCategory()]

        result = self.algorithm.get_open_rate(sender_profile, 1, categories)
        self.assertEqual(result, 0)


class V3AlgorithmTests(TestCase):
    """Tests for the V3Algorithm implementation."""

    def setUp(self):
        """Set up test fixtures."""
        self.algo_def = MockAlgorithmDefinition()
        self.algorithm = V3Algorithm(self.algo_def)

    def test_should_show_critical_alert_overlay_critical_category(self):
        """Test should_show_critical_alert_overlay with critical category."""
        categories = [MockMessageCategory(name="Critical Account/Security Alert")]
        result = self.algorithm.should_show_critical_alert_overlay(categories)
        self.assertTrue(result)

    def test_should_show_critical_alert_overlay_non_critical_category(self):
        """Test should_show_critical_alert_overlay with non-critical category."""
        categories = [MockMessageCategory(name="Regular Category")]
        result = self.algorithm.should_show_critical_alert_overlay(categories)
        self.assertFalse(result)

    def test_should_whitelist_read_fraction_action_high_score(self):
        """Test should_whitelist_read_fraction_action with high score."""
        sender_profile = MockSenderProfile()
        categories = [MockMessageCategory()]

        with patch.object(self.algorithm, "get_open_rate", return_value=0.3):
            with patch.object(self.algorithm, "_get_total_category_score", return_value=4):
                should_whitelist, include_in_digest = self.algorithm.should_whitelist_read_fraction_action(
                    sender_profile, 1, categories
                )

                self.assertTrue(should_whitelist)
                self.assertFalse(include_in_digest)

    def test_should_whitelist_read_fraction_action_moderate_score_good_rate(self):
        """Test should_whitelist_read_fraction_action with moderate score and good rate."""
        sender_profile = MockSenderProfile()
        categories = [MockMessageCategory()]

        with patch.object(self.algorithm, "get_open_rate", return_value=0.6):
            with patch.object(self.algorithm, "_get_total_category_score", return_value=2):
                should_whitelist, include_in_digest = self.algorithm.should_whitelist_read_fraction_action(
                    sender_profile, 1, categories
                )

                self.assertTrue(should_whitelist)
                self.assertFalse(include_in_digest)

    def test_should_whitelist_read_fraction_action_moderate_score_poor_rate(self):
        """Test should_whitelist_read_fraction_action with moderate score and poor rate."""
        sender_profile = MockSenderProfile()
        categories = [MockMessageCategory()]

        with patch.object(self.algorithm, "get_open_rate", return_value=0.3):
            with patch.object(self.algorithm, "_get_total_category_score", return_value=2):
                should_whitelist, include_in_digest = self.algorithm.should_whitelist_read_fraction_action(
                    sender_profile, 1, categories
                )

                self.assertFalse(should_whitelist)
                self.assertFalse(include_in_digest)

    def test_should_whitelist_read_fraction_action_low_score_good_rate(self):
        """Test should_whitelist_read_fraction_action with low score but good rate."""
        sender_profile = MockSenderProfile()
        categories = [MockMessageCategory()]

        with patch.object(self.algorithm, "get_open_rate", return_value=0.7):
            with patch.object(self.algorithm, "_get_total_category_score", return_value=1):
                should_whitelist, include_in_digest = self.algorithm.should_whitelist_read_fraction_action(
                    sender_profile, 1, categories
                )

                self.assertFalse(should_whitelist)
                self.assertTrue(include_in_digest)


class HelperFunctionsTests(TestCase):
    """Tests for helper functions in mailbot.algorithms.helpers."""

    @patch("mailbot.models.Message.objects.filter")
    def test_get_category_messages(self, mock_filter):
        """Test get_category_messages filters and annotates correctly."""
        mock_queryset = Mock()
        mock_filter.return_value.annotate.return_value.filter.return_value = mock_queryset

        sender_profile = MockSenderProfile()
        categories = [MockMessageCategory(id=1), MockMessageCategory(id=2)]

        result = get_category_messages(sender_profile, 123, categories)

        # Verify the filtering was called correctly
        mock_filter.assert_called_once_with(sender_profile=sender_profile, user_mailbot_profile_id=123)
        self.assertEqual(result, mock_queryset)

    def test_is_fts_treatment_disabled_default(self):
        """Test is_fts_treatment_disabled with default treatment."""
        preferences = {"first_time_sender_treatment": "low_priority_with_overlay"}
        result = is_fts_treatment_disabled(preferences)
        self.assertFalse(result)

    def test_is_fts_treatment_disabled_other_treatment(self):
        """Test is_fts_treatment_disabled with other treatment."""
        preferences = {"first_time_sender_treatment": "inbox"}
        result = is_fts_treatment_disabled(preferences)
        self.assertTrue(result)

    def test_is_fts_treatment_disabled_missing_preference(self):
        """Test is_fts_treatment_disabled with missing preference."""
        preferences = {}
        result = is_fts_treatment_disabled(preferences)
        self.assertFalse(result)  # Should default to enabled

    @patch("constance.config.FIRST_TIME_SENDER_OVERLAY_THRESHOLD", 10)
    def test_should_show_fts_overlay_within_limits(self):
        """Test should_show_fts_overlay when within all limits."""
        sender_profile = MockSenderProfile(metadata={"fts_overlay_sent_count": 5})
        preferences = {"first_time_sender_overlay_limit": "8"}

        result = should_show_fts_overlay(sender_profile, preferences)
        self.assertTrue(result)

    @patch("constance.config.FIRST_TIME_SENDER_OVERLAY_THRESHOLD", 10)
    def test_should_show_fts_overlay_exceeds_user_limit(self):
        """Test should_show_fts_overlay when exceeding user limit."""
        sender_profile = MockSenderProfile(metadata={"fts_overlay_sent_count": 8})
        preferences = {"first_time_sender_overlay_limit": "5"}

        result = should_show_fts_overlay(sender_profile, preferences)
        self.assertFalse(result)

    @patch("constance.config.FIRST_TIME_SENDER_OVERLAY_THRESHOLD", 10)
    def test_should_show_fts_overlay_exceeds_global_limit(self):
        """Test should_show_fts_overlay when exceeding global limit."""
        sender_profile = MockSenderProfile(metadata={"fts_overlay_sent_count": 15})
        preferences = {"first_time_sender_overlay_limit": "no-limit"}

        result = should_show_fts_overlay(sender_profile, preferences)
        self.assertFalse(result)

    @patch("constance.config.FIRST_TIME_SENDER_OVERLAY_THRESHOLD", 10)
    def test_should_show_fts_overlay_no_limit(self):
        """Test should_show_fts_overlay with no user limit."""
        sender_profile = MockSenderProfile(metadata={"fts_overlay_sent_count": 5})
        preferences = {"first_time_sender_overlay_limit": "no-limit"}

        result = should_show_fts_overlay(sender_profile, preferences)
        self.assertTrue(result)

    @patch("constance.config.FIRST_TIME_SENDER_OVERLAY_THRESHOLD", 10)
    def test_should_show_fts_overlay_invalid_user_limit(self):
        """Test should_show_fts_overlay with invalid user limit."""
        sender_profile = MockSenderProfile(metadata={"fts_overlay_sent_count": 5})
        preferences = {"first_time_sender_overlay_limit": "invalid"}

        with patch("mailbot.algorithms.helpers.logger.error") as mock_logger:
            result = should_show_fts_overlay(sender_profile, preferences)
            self.assertFalse(result)
            mock_logger.assert_called_once()


class UtilFunctionsTests(TestCase):
    """Tests for utility functions in mailbot.algorithms.utils."""

    def test_get_algorithm_instance_active_algorithm(self):
        """Test get_algorithm_instance with active algorithm."""
        algo_def = MockAlgorithmDefinition(version=1, is_active=True)
        user_profile = MockUserMailBotProfile(algorithm_definition=algo_def)

        with patch.dict(ALGORITHM_MAP, {AlgorithmVersion.v1: V1Algorithm}):
            result = get_algorithm_instance(user_profile)
            self.assertIsInstance(result, V1Algorithm)
            self.assertEqual(result.algo_def, algo_def)

    def test_get_algorithm_instance_inactive_algorithm(self):
        """Test get_algorithm_instance with inactive algorithm falls back to default."""
        algo_def = MockAlgorithmDefinition(version=2, is_active=False)
        user_profile = MockUserMailBotProfile(algorithm_definition=algo_def)
        default_algo_def = MockAlgorithmDefinition(version=1, is_active=True)

        with patch("mailbot.algorithms.utils.get_default_active_algorithm_def", return_value=default_algo_def):
            with patch.dict(ALGORITHM_MAP, {AlgorithmVersion.v1: V1Algorithm}):
                result = get_algorithm_instance(user_profile)
                self.assertIsInstance(result, V1Algorithm)
                self.assertEqual(result.algo_def, default_algo_def)

    def test_get_algorithm_instance_no_algorithm(self):
        """Test get_algorithm_instance with no algorithm falls back to default."""
        user_profile = MockUserMailBotProfile(algorithm_definition=None)
        default_algo_def = MockAlgorithmDefinition(version=1, is_active=True)

        with patch("mailbot.algorithms.utils.get_default_active_algorithm_def", return_value=default_algo_def):
            with patch.dict(ALGORITHM_MAP, {AlgorithmVersion.v1: V1Algorithm}):
                result = get_algorithm_instance(user_profile)
                self.assertIsInstance(result, V1Algorithm)
                self.assertEqual(result.algo_def, default_algo_def)

    def test_get_algorithm_instance_invalid_version(self):
        """Test get_algorithm_instance with invalid version raises ValueError."""
        algo_def = MockAlgorithmDefinition(version=999, is_active=True)
        user_profile = MockUserMailBotProfile(algorithm_definition=algo_def)

        with self.assertRaises(ValueError) as context:
            get_algorithm_instance(user_profile)

        self.assertIn("Algorithm version '999' not found", str(context.exception))

    @patch("mailbot.models.AlgorithmDefinition.objects.get")
    def test_get_default_active_algorithm_def_success(self, mock_get):
        """Test get_default_active_algorithm_def returns correct definition."""
        expected_def = MockAlgorithmDefinition()
        mock_get.return_value = expected_def

        result = get_default_active_algorithm_def()

        self.assertEqual(result, expected_def)
        mock_get.assert_called_once_with(version=DEFAULT_ALGORITHM_VERSION.value, is_active=True)

    @patch("mailbot.models.AlgorithmDefinition.objects.get")
    def test_get_default_active_algorithm_def_not_found(self, mock_get):
        """Test get_default_active_algorithm_def raises ValueError when not found."""
        from mailbot.models import AlgorithmDefinition

        mock_get.side_effect = AlgorithmDefinition.DoesNotExist()

        with self.assertRaises(ValueError) as context:
            get_default_active_algorithm_def()

        self.assertIn("Default active algorithm definition", str(context.exception))


class AlgorithmConstantsTests(TestCase):
    """Tests for algorithm constants."""

    def test_algorithm_map_contains_all_versions(self):
        """Test that ALGORITHM_MAP contains all algorithm versions."""
        expected_versions = [AlgorithmVersion.v1, AlgorithmVersion.v2, AlgorithmVersion.v3]

        for version in expected_versions:
            self.assertIn(version, ALGORITHM_MAP)

    def test_algorithm_map_classes(self):
        """Test that ALGORITHM_MAP maps to correct classes."""
        self.assertEqual(ALGORITHM_MAP[AlgorithmVersion.v1], V1Algorithm)
        self.assertEqual(ALGORITHM_MAP[AlgorithmVersion.v2], V2Algorithm)
        self.assertEqual(ALGORITHM_MAP[AlgorithmVersion.v3], V3Algorithm)

    def test_default_algorithm_version(self):
        """Test that DEFAULT_ALGORITHM_VERSION is v1."""
        self.assertEqual(DEFAULT_ALGORITHM_VERSION, AlgorithmVersion.v1)
