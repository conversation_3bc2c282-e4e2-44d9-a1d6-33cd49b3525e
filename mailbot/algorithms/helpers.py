import logging
from typing import List
from mailbot.models import Message, MessageCategory
from django.contrib.postgres.aggregates import <PERSON><PERSON>y<PERSON>gg
from constance import config as constance_config

logger = logging.getLogger(__name__)


def get_category_messages(sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]):
    """
    Get messages from a sender that match specific categories.
    This function filters messages by sender profile and mailbot profile,
    then filters them to only include messages that
    have exactly the specified categories.

    Args:
        sender_profile: The sender profile to filter messages for.
        mailbot_profile_id: The mailbot profile ID to filter by.
        message_categories (List[MessageCategory]): List of categories that messages must have.

    Returns:
        QuerySet: A Django QuerySet of Message objects that match the criteria.
    """
    cat_ids = sorted([category.id for category in message_categories])
    return (
        Message.objects.filter(sender_profile=sender_profile, user_mailbot_profile_id=mailbot_profile_id)
        .annotate(cat_ids=ArrayAgg("categories", ordering="categories__id"))
        .filter(cat_ids=cat_ids)
    )


def is_fts_treatment_disabled(user_preferences):
    """
    Check if first-time sender treatment is disabled in user preferences.

    Args:
        user_preferences (dict): Dictionary containing user preferences.

    Returns:
        bool: True if first-time sender treatment is disabled, False otherwise.
    """
    treatment = user_preferences.get("first_time_sender_treatment", "low_priority_with_overlay")
    return treatment != "low_priority_with_overlay"


def should_show_fts_overlay(sender_profile, user_preferences):
    """
    Determine if a first-time sender overlay should be shown.

    Args:
        sender_profile: The sender profile containing overlay metadata.
        user_preferences (dict): Dictionary containing user preferences.

    Returns:
        bool: True if the overlay should be shown, False otherwise.
    """
    sent_count = sender_profile.metadata.get("fts_overlay_sent_count", 0)
    global_limit = constance_config.FIRST_TIME_SENDER_OVERLAY_THRESHOLD
    user_limit = user_preferences.get("first_time_sender_overlay_limit", "no-limit")
    try:
        if user_limit == "no-limit" or int(user_limit) > sent_count:
            return sent_count < global_limit
    except ValueError as e:
        logger.error(f"Error in first time sender filter: {e}")
    return False
