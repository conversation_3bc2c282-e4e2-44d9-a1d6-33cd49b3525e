from typing import List, Optional, Tuple
from mailbot.algorithms.helpers import (
    get_category_messages,
    is_fts_treatment_disabled,
    should_show_fts_overlay,
)
from mailbot.models import MailBotGenericLabel, MessageCategory, SenderProfile, UserMailBotProfile
from mailbot.algorithms.base import BaseAlgorithm
from django.db.models import Count, Q


class V2Algorithm(BaseAlgorithm):

    def should_send_fts_overlay(self, sender_profile: SenderProfile = None) -> bool:
        """
        Determine if a first-time sender overlay should be sent.

        V2 algorithm implements sophisticated logic that considers user preferences,
        onboarding status, and overlay limits.

        Args:
            sender_profile (SenderProfile, optional): The sender profile to evaluate.
                If None, returns True (default behavior).

        Returns:
            bool: True if overlay should be sent, False otherwise.
        """
        if sender_profile:
            user_preferences = sender_profile.user_mailbot_profile.preferences
            if sender_profile.user_mailbot_profile.is_onboarding_invalid:
                return False
            elif is_fts_treatment_disabled(user_preferences):
                return False
            elif should_show_fts_overlay(sender_profile, user_preferences):
                return True
            else:
                return False
        return True

    def should_show_critical_alert_overlay(self, message_categories: List[MessageCategory]) -> bool:
        """
        Determine if a critical alert overlay should be shown.
        V2 algorithm never shows critical (lucene) alert overlays.

        Args:
            message_categories (List[MessageCategory]): List of message categories (not used in v2).

        Returns:
            bool: Always returns False in V2 algorithm.
        """
        return False

    def default_action_label(
        self, user_mailbot_profile: UserMailBotProfile, message_categories: List[MessageCategory]
    ) -> MailBotGenericLabel:
        """
        Determine the default action label based on message category importance.

        V2 algorithm uses category scoring to determine if a message should be
        whitelisted or zapped, ignoring user preferences for first-time sender treatment.

        Args:
            user_mailbot_profile (UserMailBotProfile): The user's mailbot profile (not used in v2).
            message_categories (List[MessageCategory]): List of message categories to evaluate.

        Returns:
            MailBotGenericLabel: WHITE_LIST if message has important categories, ZAPPED otherwise.
        """
        return (
            MailBotGenericLabel.WHITE_LIST
            if self._has_important_categories(message_categories)
            else MailBotGenericLabel.ZAPPED
        )

    def should_whitelist_read_fraction_action(
        self, sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]
    ) -> Tuple[bool, Optional[bool]]:
        """
        Determine if sender should be whitelisted based on read fraction and category importance.

        V2 algorithm requires both a minimum open rate (25%) AND important categories
        for whitelisting. Messages with very high open rate (>80%) but not whitelisted
        are included in digest.

        Args:
            sender_profile: The sender profile containing read statistics.
            mailbot_profile_id: The mailbot profile ID.
            message_categories (List[MessageCategory]): List of message categories to evaluate.

        Returns:
            Tuple[bool, Optional[bool]]: A tuple containing:
                - should_whitelist: True if open rate >= 25% AND has important categories
                - include_in_digest: True if open rate > 80% and not whitelisted
        """
        open_rate = self.get_open_rate(sender_profile, mailbot_profile_id, message_categories)
        has_important_categories = self._has_important_categories(message_categories)
        should_whitelist = open_rate >= 0.25 and has_important_categories
        include_in_digest = open_rate > 0.8 and not should_whitelist
        return should_whitelist, include_in_digest

    def get_total_message_counts(
        self, sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]
    ) -> int:
        """
        Get the total count of messages from this sender with matching categories.

        V2 algorithm filters messages by categories before counting, providing
        more accurate counts for category-specific analysis.

        Args:
            sender_profile: The sender profile to count messages for.
            mailbot_profile_id: The mailbot profile ID.
            message_categories (List[MessageCategory]): List of message categories to filter by.

        Returns:
            int: The count of distinct messages matching the specified categories.
        """
        return get_category_messages(sender_profile, mailbot_profile_id, message_categories).distinct().count()

    def get_open_rate(self, sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]) -> float:
        """
        Calculate the open rate for messages from this sender with matching categories.

        V2 algorithm calculates open rate based only on messages that match the specified
        categories, providing more accurate category-specific open rates.

        Args:
            sender_profile: The sender profile to calculate open rate for.
            mailbot_profile_id: The mailbot profile ID.
            message_categories (List[MessageCategory]): List of message categories to filter by.

        Returns:
            float: The open rate as a decimal between 0 and 1. Returns 0 if no messages found.
        """
        counts = get_category_messages(sender_profile, mailbot_profile_id, message_categories).aggregate(
            total_count=Count("id"), total_read=Count("id", filter=Q(is_read=True))
        )
        return counts["total_read"] / counts["total_count"] if counts["total_count"] > 0 else 0
