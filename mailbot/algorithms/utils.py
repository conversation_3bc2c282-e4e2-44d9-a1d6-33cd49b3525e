import logging
from mailbot.algorithms.base import BaseAlgorithm
from mailbot.algorithms.constants import ALG<PERSON><PERSON>HM_MAP, DEFAULT_ALGORITHM_VERSION
from mailbot.models import AlgorithmDefinition, AlgorithmVersion, UserMailBotProfile

logger = logging.getLogger(__name__)


def get_algorithm_instance(user_profile: UserMailBotProfile) -> BaseAlgorithm:
    algo_def_to_use = user_profile.algorithm_definition
    if algo_def_to_use and algo_def_to_use.is_active:
        try:
            algorithm_version = AlgorithmVersion(algo_def_to_use.version)
            algorithm_klass = ALGORITHM_MAP[algorithm_version]
        except ValueError:
            logger.warning(f"Warning: Algorithm version '{algo_def_to_use.version}' not found.")
            raise ValueError(f"Algorithm version '{algo_def_to_use.version}' not found.")
    else:
        warning_message = f"Algorithm version '{algo_def_to_use.version if algo_def_to_use else 'None'}' "
        warning_message += "is inactive." if algo_def_to_use and not algo_def_to_use.is_active else "not found."
        logger.warning(f"Warning: Falling back to default algorithm: {DEFAULT_ALGORITHM_VERSION} {warning_message}")
        algo_def_to_use = get_default_active_algorithm_def()
        algorithm_klass = ALGORITHM_MAP[DEFAULT_ALGORITHM_VERSION]
    return algorithm_klass(algo_def=algo_def_to_use)


def get_default_active_algorithm_def() -> AlgorithmDefinition:
    try:
        return AlgorithmDefinition.objects.get(version=DEFAULT_ALGORITHM_VERSION.value, is_active=True)
    except AlgorithmDefinition.DoesNotExist:
        logger.warning(f"Warning: Default active algorithm '{DEFAULT_ALGORITHM_VERSION}' not found.")
        raise ValueError(f"Default active algorithm definition '{DEFAULT_ALGORITHM_VERSION}' not found.")
