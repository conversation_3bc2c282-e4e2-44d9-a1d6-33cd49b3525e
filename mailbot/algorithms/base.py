from abc import ABC, abstractmethod
from typing import List, <PERSON><PERSON>, Tuple
from mailbot.models import (
    AlgorithmCategoryScore,
    AlgorithmDefinition,
    MailBotGenericLabel,
    MessageCategory,
    SenderProfile,
    UserMailBotProfile,
)


class BaseAlgorithm(ABC):
    """
    Abstract base class for all MailBot algorithms.

    This class defines the interface that all algorithm implementations must follow.
    It provides common functionality for calculating category scores and defines
    abstract methods that must be implemented by concrete algorithm classes.

    Attributes:
        algo_def (AlgorithmDefinition): The algorithm definition instance for user mailbot profile.
    """

    def __init__(self, algo_def: AlgorithmDefinition):
        """
        Initialize the algorithm with its definition.
        """
        self.algo_def = algo_def

    @property
    def version(self) -> int:
        """
        Get the algorithm version number.
        """
        return self.algo_def.version

    def _has_important_categories(self, message_categories: List[MessageCategory]) -> bool:
        """
        Check if the message has important categories based on total score.
        """
        return self._get_total_category_score(message_categories) >= 2

    def _get_total_category_score(self, message_categories: List[MessageCategory]) -> int:
        """
        Calculate the total score for all message categories.
        This method sums up the scores for all categories, using algorithm-specific
        scores when available, otherwise falling back to default category scores.

        Args:
            message_categories (List[MessageCategory]): List of message categories
                to score.

        Returns:
            int: The total score for all categories.
        """
        cat_ids = [cat.id for cat in message_categories]
        scores = AlgorithmCategoryScore.objects.filter(algorithm_definition=self.algo_def, category_id__in=cat_ids)
        score_map = {s.category_id: s.score for s in scores}
        return sum(score_map.get(cat.id, cat.score) for cat in message_categories)

    @abstractmethod
    def get_total_message_counts(
        self, sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]
    ) -> int:
        """
        Get the total count of messages for the given parameters.

        This method must be implemented by concrete algorithm classes to define
        how message counts are calculated.

        Args:
            sender_profile: The sender profile to count messages for.
            mailbot_profile_id: The mailbot profile ID.
            message_categories (List[MessageCategory]): List of message categories
                to filter by.

        Returns:
            int: The total count of messages.
        """
        pass

    def should_send_fts_overlay(self, sender_profile: SenderProfile) -> bool:
        """
        Determine if a first-time sender overlay should be sent.

        Args:
            sender_profile (SenderProfile): The sender profile to evaluate.

        Returns:
            bool: True if overlay should be sent, False otherwise.
        """
        return False

    def should_show_critical_alert_overlay(self, message_categories: List[MessageCategory]) -> bool:
        """
        Determine if a critical (lucene) alert overlay should be shown.

        Args:
            message_categories (List[MessageCategory]): List of message categories
                to evaluate.

        Returns:
            bool: True if critical alert overlay should be shown, False otherwise.
        """
        return False

    def default_action_label(
        self, user_mailbot_profile: UserMailBotProfile, message_categories: List[MessageCategory]
    ) -> MailBotGenericLabel:
        """
        Determine the default action label for a message.

        Args:
            user_mailbot_profile (UserMailBotProfile): The user's mailbot profile.
            message_categories (List[MessageCategory]): List of message categories.

        Returns:
            MailBotGenericLabel: The default label to apply to the message.
        """
        return MailBotGenericLabel.ZAPPED

    def should_whitelist_read_fraction_action(
        self, sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]
    ) -> Tuple[bool, bool]:
        """
        Determine if sender should be whitelisted based on read fraction.

        Args:
            sender_profile: The sender profile to evaluate.
            mailbot_profile_id: The mailbot profile ID.
            message_categories (List[MessageCategory]): List of message categories.

        Returns:
            Tuple[bool, bool]: A tuple containing:
                - should_whitelist: Whether the sender should be whitelisted
                - include_in_digest: Whether to include in digest
        """
        return False, False

    def get_open_rate(self, sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]) -> float:
        """
        Calculate the open rate for messages from this sender.

        Args:
            sender_profile: The sender profile to calculate open rate for.
            mailbot_profile_id: The mailbot profile ID.
            message_categories (List[MessageCategory]): List of message categories.

        Returns:
            float: The open rate as a decimal between 0 and 1.
        """
        return 0
