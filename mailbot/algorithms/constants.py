from typing import Type, Dict

from mailbot.models import AlgorithmVersion
from .base import BaseAlgorithm
from .v1_algorithm import V1Algorithm
from .v2_algorithm import V2Algorithm
from .v3_algorithm import V3Algorithm

# TODO to implement auto discovery later
ALGORITHM_MAP: Dict[AlgorithmVersion, Type[BaseAlgorithm]] = {
    AlgorithmVersion.v1: V1Algorithm,
    AlgorithmVersion.v2: V2Algorithm,
    AlgorithmVersion.v3: V3Algorithm,
}

"""
The default algorithm version to use when no specific version is configured
or when the configured version is not available.
"""
DEFAULT_ALGORITHM_VERSION = AlgorithmVersion.v1
