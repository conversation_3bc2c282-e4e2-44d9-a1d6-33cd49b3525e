from typing import List, Optional, Tuple
from mailbot.algorithms.helpers import (
    get_category_messages,
    is_fts_treatment_disabled,
    should_show_fts_overlay,
)
from mailbot.models import MailBotGenericLabel, MessageCategory, SenderProfile, UserMailBotProfile
from mailbot.algorithms.base import BaseAlgorithm
from django.db.models import Count, Q


class V3Algorithm(BaseAlgorithm):
    def should_send_fts_overlay(self, sender_profile: SenderProfile = None) -> bool:
        """
        Determine if a first-time sender overlay should be sent.

        V3 algorithm uses the same sophisticated logic as V2, considering user preferences,
        onboarding status, and overlay limits.

        Args:
            sender_profile (SenderProfile, optional): The sender profile to evaluate.
                If None, returns True (default behavior).

        Returns:
            bool: True if overlay should be sent, False otherwise.
        """
        if sender_profile:
            user_preferences = sender_profile.user_mailbot_profile.preferences
            if sender_profile.user_mailbot_profile.is_onboarding_invalid:
                return False
            elif is_fts_treatment_disabled(user_preferences):
                return False
            elif should_show_fts_overlay(sender_profile, user_preferences):
                return True
            else:
                return False
        return True

    def should_show_critical_alert_overlay(self, message_categories: List[MessageCategory]) -> bool:
        """
        Determine if a critical alert overlay should be shown.

        V3 algorithm shows critical alert overlays only for messages with
        "Critical Account/Security Alert" category.

        Args:
            message_categories (List[MessageCategory]): List of message categories to evaluate.

        Returns:
            bool: True if any category is "Critical Account/Security Alert", False otherwise.
        """
        return any(category.name == "Critical Account/Security Alert" for category in message_categories)

    def default_action_label(
        self, user_mailbot_profile: UserMailBotProfile, message_categories: List[MessageCategory]
    ) -> MailBotGenericLabel:
        """
        Determine the default action label based on message category importance.

        V3 algorithm uses the same category-based scoring as V2 to determine
        if a message should be whitelisted or zapped.

        Args:
            user_mailbot_profile (UserMailBotProfile): The user's mailbot profile (not used in v3).
            message_categories (List[MessageCategory]): List of message categories to evaluate.

        Returns:
            MailBotGenericLabel: WHITE_LIST if message has important categories, ZAPPED otherwise.
        """
        return (
            MailBotGenericLabel.WHITE_LIST
            if self._has_important_categories(message_categories)
            else MailBotGenericLabel.ZAPPED
        )

    def should_whitelist_read_fraction_action(
        self, sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]
    ) -> Tuple[bool, Optional[bool]]:
        """
        Determine if sender should be whitelisted based on advanced scoring criteria.

        V3 algorithm uses dual criteria for whitelisting:
        1. High category score (>=4), OR
        2. Moderate category score (>=2) AND good open rate (>=50%)

        Messages with good open rate (>=50%) but not whitelisted are included in digest.

        Args:
            sender_profile: The sender profile containing read statistics.
            mailbot_profile_id: The mailbot profile ID.
            message_categories (List[MessageCategory]): List of message categories to evaluate.

        Returns:
            Tuple[bool, Optional[bool]]: A tuple containing:
                - should_whitelist: True if score >= 4 OR (score >= 2 AND open_rate >= 50%)
                - include_in_digest: True if open rate >= 50% and not whitelisted
        """
        open_rate = self.get_open_rate(sender_profile, mailbot_profile_id, message_categories)
        score = self._get_total_category_score(message_categories)
        should_whitelist = score >= 4 or (score >= 2 and open_rate >= 0.5)
        include_in_digest = open_rate >= 0.5 and not should_whitelist
        return should_whitelist, include_in_digest

    def get_total_message_counts(
        self, sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]
    ) -> int:
        """
        Get the total count of messages from this sender with matching categories.

        V3 algorithm uses the same category-filtered counting as V2, providing
        accurate counts for category-specific analysis.

        Args:
            sender_profile: The sender profile to count messages for.
            mailbot_profile_id: The mailbot profile ID.
            message_categories (List[MessageCategory]): List of message categories to filter by.

        Returns:
            int: The count of distinct messages matching the specified categories.
        """
        return get_category_messages(sender_profile, mailbot_profile_id, message_categories).distinct().count()

    def get_open_rate(self, sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]) -> float:
        """
        Calculate the open rate for messages from this sender with matching categories.

        V3 algorithm uses the same category-filtered open rate calculation as V2,
        providing accurate category-specific open rates.

        Args:
            sender_profile: The sender profile to calculate open rate for.
            mailbot_profile_id: The mailbot profile ID.
            message_categories (List[MessageCategory]): List of message categories to filter by.

        Returns:
            float: The open rate as a decimal between 0 and 1. Returns 0 if no messages found.
        """
        counts = get_category_messages(sender_profile, mailbot_profile_id, message_categories).aggregate(
            total_count=Count("id"), total_read=Count("id", filter=Q(is_read=True))
        )
        return counts["total_read"] / counts["total_count"] if counts["total_count"] > 0 else 0
