from typing import List, Tu<PERSON>, Optional
from mailbot.algorithms.base import BaseAlgorithm
from mailbot.models import Message<PERSON>ategor<PERSON>, UserMailBotProfile, MailBotGenericLabel


class V1Algorithm(BaseAlgorithm):
    def default_action_label(
        self, user_mailbot_profile: UserMailBotProfile, message_categories: List[MessageCategory]
    ) -> MailBotGenericLabel:
        """
        Determine the default action label based on user's first-time sender treatment preference.

        Args:
            user_mailbot_profile (UserMailBotProfile): The user's mailbot profile containing preferences.
            message_categories (List[MessageCategory]): List of message categories (not used in v1).

        Returns:
            MailBotGenericLabel: WHITE_LIST if user prefers "inbox" treatment, ZAPPED otherwise.
        """
        first_time_sender_treatment = user_mailbot_profile.preferences["first_time_sender_treatment"]
        return MailBotGenericLabel.WHITE_LIST if first_time_sender_treatment == "inbox" else MailBotGenericLabel.ZAPPED

    def should_show_critical_alert_overlay(self, message_categories: List[MessageCategory]) -> bool:
        """
        Determine if a critical (lucene) alert overlay should be shown.
        In V1 algorithm, critical alert overlays are always shown regardless of categories.

        Args:
            message_categories (List[MessageCategory]): List of message categories (not used in v1).

        Returns:
            bool: Always returns True in V1 algorithm.
        """
        return True

    def should_whitelist_read_fraction_action(
        self, sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]
    ) -> Tuple[bool, Optional[bool]]:
        """
        Determine if sender should be whitelisted based on read fraction.

        V1 algorithm uses a high threshold (90%) for whitelisting and includes
        messages in digest if open rate is >= 30% but below whitelist threshold.

        Args:
            sender_profile: The sender profile containing read statistics.
            mailbot_profile_id: The mailbot profile ID (not used in v1).
            message_categories (List[MessageCategory]): List of message categories (not used in v1).

        Returns:
            Tuple[bool, Optional[bool]]: A tuple containing:
                - should_whitelist: True if open rate >= 90%
                - include_in_digest: True if open rate >= 30% and not whitelisted
        """
        open_rate = self.get_open_rate(sender_profile, mailbot_profile_id, message_categories)
        should_whitelist = open_rate >= 0.9
        include_in_digest = open_rate >= 0.3 and not should_whitelist
        return should_whitelist, include_in_digest

    def get_total_message_counts(
        self, sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]
    ) -> int:
        """
        Get the total count of messages from this sender.

        V1 algorithm simply returns the total count from the sender profile.

        Args:
            sender_profile: The sender profile containing message statistics.
            mailbot_profile_id: The mailbot profile ID (not used in v1).
            message_categories (List[MessageCategory]): List of message categories (not used in v1).

        Returns:
            int: The total count of messages from this sender.
        """
        return sender_profile.total_count

    def get_open_rate(self, sender_profile, mailbot_profile_id, message_categories: List[MessageCategory]) -> float:
        """
        Calculate the open rate for messages from this sender.

        V1 algorithm calculates open rate as read_count / (total_count - 1) to exclude
        the current message from the calculation.

        Args:
            sender_profile: The sender profile containing read and total counts.
            mailbot_profile_id: The mailbot profile ID (not used in v1).
            message_categories (List[MessageCategory]): List of message categories (not used in v1).

        Returns:
            float: The open rate as a decimal between 0 and 1. Returns 0 if total_count <= 1
                to avoid division by zero.
        """
        if sender_profile.total_count <= 1:
            return 0.0
        return sender_profile.read_count / (sender_profile.total_count - 1)
