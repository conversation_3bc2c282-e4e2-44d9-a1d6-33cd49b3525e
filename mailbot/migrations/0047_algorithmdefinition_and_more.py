# Generated by Django 4.2.5 on 2025-05-30 07:11

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import django_extensions.db.fields


class Migration(migrations.Migration):

    dependencies = [
        ('mailbot', '0046_alter_usermailbotprofile_secondary_profile_preferences'),
    ]

    operations = [
        migrations.CreateModel(
            name='AlgorithmDefinition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('version', models.PositiveIntegerField(choices=[(1, 'v1'), (2, 'v2'), (3, 'v3')], unique=True)),
                ('description', models.TextField(blank=True, help_text='Optional detailed description of the algorithm.')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this algorithm is active or not')),
            ],
            options={
                'get_latest_by': 'modified',
                'abstract': False,
            },
        ),
        migrations.RemoveField(
            model_name='usermailbotprofile',
            name='algo_version',
        ),
        migrations.AlterField(
            model_name='messagecategory',
            name='score',
            field=models.PositiveSmallIntegerField(default=0, help_text='Default score for the message category (0 to 2). Algorithms can override this.', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(2)]),
        ),
        migrations.CreateModel(
            name='AlgorithmCategoryScore',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created', django_extensions.db.fields.CreationDateTimeField(auto_now_add=True, verbose_name='created')),
                ('modified', django_extensions.db.fields.ModificationDateTimeField(auto_now=True, verbose_name='modified')),
                ('score', models.IntegerField(help_text='The specific score this algorithm assigns to this category. If not define, we will fallback to the default category score')),
                ('algorithm_definition', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mailbot.algorithmdefinition')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='mailbot.messagecategory')),
            ],
        ),
        migrations.AddField(
            model_name='usermailbotprofile',
            name='algorithm_definition',
            field=models.ForeignKey(blank=True, help_text='Specifies which algorithm to use for this user.', null=True, on_delete=django.db.models.deletion.SET_NULL, to='mailbot.algorithmdefinition'),
        ),
        migrations.AddConstraint(
            model_name='algorithmcategoryscore',
            constraint=models.UniqueConstraint(fields=('algorithm_definition', 'category'), name='unique_algo_category_score'),
        ),
    ]
